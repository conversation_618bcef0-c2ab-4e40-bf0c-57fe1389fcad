import React, { useEffect, useRef, useState } from 'react';
import { useLocalParticipant } from '@livekit/components-react';

function AudioVisualizer({
  className = '',
  barCount = 5,
  height = 20,
  width = 40,
  color = '#0a84ff',
  sensitivity = 1.5
}) {
  const [audioLevels, setAudioLevels] = useState(new Array(barCount).fill(0));
  const [isActive, setIsActive] = useState(false);
  const animationRef = useRef();
  const analyserRef = useRef();
  const dataArrayRef = useRef();
  const { localParticipant } = useLocalParticipant();

  useEffect(() => {
    let audioContext;
    let sourceNode;

    const startVisualization = () => {
      const updateLevels = () => {
        if (!analyserRef.current || !dataArrayRef.current) return;

        analyserRef.current.getByteFrequencyData(dataArrayRef.current);
        
        // Calculate average volume
        const average = dataArrayRef.current.reduce((sum, value) => sum + value, 0) / dataArrayRef.current.length;
        const normalizedLevel = Math.min(average / 128 * sensitivity, 1);

        // Create animated bars with some randomness for visual appeal
        const newLevels = new Array(barCount).fill(0).map((_, index) => {
          const baseLevel = normalizedLevel;
          const variation = (Math.random() - 0.5) * 0.3;
          const level = Math.max(0, Math.min(1, baseLevel + variation));
          
          // Add some frequency-based variation
          const freqIndex = Math.floor((index / barCount) * dataArrayRef.current.length);
          const freqLevel = dataArrayRef.current[freqIndex] / 255;
          
          return Math.max(level * 0.7, freqLevel * 0.3);
        });

        setAudioLevels(newLevels);
        animationRef.current = requestAnimationFrame(updateLevels);
      };
      updateLevels();
    };

    const setupAudioAnalysis = async () => {
      try {
        if (!localParticipant) return;

        // Get the microphone track
        const micTrack = localParticipant.getTrackPublication('microphone')?.track;
        if (!micTrack || !micTrack.mediaStream) return;

        // Create audio context and analyser
        audioContext = new (window.AudioContext || window.webkitAudioContext)();
        analyserRef.current = audioContext.createAnalyser();
        analyserRef.current.fftSize = 256;
        analyserRef.current.smoothingTimeConstant = 0.8;

        const bufferLength = analyserRef.current.frequencyBinCount;
        dataArrayRef.current = new Uint8Array(bufferLength);

        // Connect microphone stream to analyser
        sourceNode = audioContext.createMediaStreamSource(micTrack.mediaStream);
        sourceNode.connect(analyserRef.current);

        setIsActive(true);
        startVisualization();
      } catch (error) {
        console.error('Error setting up audio analysis:', error);
        setIsActive(false);
      }
    };

    // Setup audio analysis when microphone is enabled
    if (localParticipant?.isMicrophoneEnabled) {
      setupAudioAnalysis();
    } else {
      setIsActive(false);
      setAudioLevels(new Array(barCount).fill(0));
    }

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
      if (audioContext && audioContext.state !== 'closed') {
        audioContext.close();
      }
      if (sourceNode) {
        sourceNode.disconnect();
      }
    };
  }, [localParticipant?.isMicrophoneEnabled, barCount, sensitivity]);

  return (
    <div 
      className={`audio-visualizer ${className}`}
      style={{
        display: 'flex',
        alignItems: 'flex-end',
        justifyContent: 'center',
        height: `${height}px`,
        width: `${width}px`,
        gap: '2px',
        padding: '2px'
      }}
    >
      {audioLevels.map((level, index) => (
        <div
          key={index}
          className="audio-visualizer__bar"
          style={{
            width: `${Math.floor(width / barCount) - 2}px`,
            height: `${Math.max(2, level * height)}px`,
            backgroundColor: isActive && level > 0.1 ? color : '#666',
            borderRadius: '1px',
            transition: 'height 0.1s ease-out, background-color 0.2s ease',
            opacity: isActive ? 1 : 0.5
          }}
        />
      ))}
    </div>
  );
}

export default AudioVisualizer;
